// "//-----------------------------------------------------------------------".
// <copyright file="ErrorActivatingAquilaChannelRequestChangeStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <inheritdoc/>
    public class CreationErrorAquilaChannelRequestChangeStrategy : IAquilaRequestChangeStrategy
    {
        /// <summary>
        /// The media service.
        /// </summary>
        private readonly IAquilaClientService aquilaClientService;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<CreationErrorAquilaChannelRequestChangeStrategy> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="CreationErrorAquilaChannelRequestChangeStrategy"/> class.
        /// </summary>
        /// <param name="aquilaClientService">The media service.</param>
        /// <param name="telemetryService"> The telemetry service.</param>
        /// <param name="logger">The logger.</param>
        public CreationErrorAquilaChannelRequestChangeStrategy(IAquilaClientService aquilaClientService, ITelemetryService telemetryService, ILogger<CreationErrorAquilaChannelRequestChangeStrategy> logger)
        {
            this.aquilaClientService = aquilaClientService;
            this.telemetryService = telemetryService;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public AquilaChannelState ChannelState => AquilaChannelState.CreationError;

        /// <inheritdoc/>
        public async Task RequestChangeAsync(AquilaChannelState desiredAquilaChannelState, string instanceId, string channelId, string eventId)
        {
            switch (desiredAquilaChannelState)
            {
                case AquilaChannelState.Started:
                    // nothing
                    break;
                case AquilaChannelState.Starting:
                    // nothing
                    break;
                case AquilaChannelState.Stopping:
                    await this.aquilaClientService.StopChannelInstanceAsync(channelId, instanceId).ConfigureAwait(false);
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToStopping, EventData.CorrelationTag);
                    break;
                case AquilaChannelState.Stopped:
                    await this.aquilaClientService.StopChannelInstanceAsync(channelId, instanceId).ConfigureAwait(false);
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToStopped, EventData.CorrelationTag);
                    break;
                case AquilaChannelState.Deleted:
                    // nothing - CreationErrorAquilaChannelRequestChangeStrategy does not support deletion
                    this.logger.LogWarning("CreationErrorAquilaChannelRequestChangeStrategy: Deletion requested for channel {ChannelId} but this strategy does not support deletion", channelId);
                    break;
                case AquilaChannelState.CreationError:
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToCreationError, EventData.CorrelationTag);
                    break;
                default:
                    throw new NotSupportedException();
            }
        }
    }
}