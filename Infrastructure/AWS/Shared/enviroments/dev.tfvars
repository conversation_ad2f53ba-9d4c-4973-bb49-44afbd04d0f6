environment                     = "dev"
vpc_id                          = "vpc-bb39b9c0"
subnet_ids                      = ["subnet-d59ea988", "subnet-0810c0cf91bf31279"]
security_group_ids              = ["sg-041435d71499c606e"]
docdb_instance_class            = "db.t3.medium"
docdb_instance_count            = 2
master_username                 = "docdbuser"
aws_region                      = "us-east-1"
dmm_endpoint                    = "http://lvcmanager-qa.nba-hq.com/"
gmswatchdog_endpoint            = "https://orchestrator-gmswatchdog-dev.internal.nba.com"
scheduler_endpoint              = "https://orchestrator-scheduler-dev.internal.nba.com"
tvp_endpoint                    = "https://appgw-boss.prodc.nba.tv3cloud.com/oss"
aquila_endpoint                 = "https://aquila-preprod.aas.mediakind.com/"
quortex_endpoint                = "https://api.quortex.io/"
prismaworker_endpoint           = "https://nba-fd-prodc-eastus2.azurefd.net/scte224"
prismamanager_endpoint          = "https://nba-fd-prodc-eastus2.azurefd.net/scte224"
aquila_settings_accountid       = "2ef047eb-ae7b-4478-9fcd-2e38f58e965c"
aquila_settings_accountname     = "NBAAlpha"
aquila_settings_useremail       = "<EMAIL>"
aquila_settings_authendpoint    = "https://hub-preprod.aas.mediakind.com/api/v1.0/auth/login"
dmm_settings_accountid          = "null"
# Note: We're not going to use the quortex pools in dev, so setting them to null
quortex_pool_uuid               = "null"
quortex_pool_uuid_dr            = "null"
quortex_pool_uuid_radio         = "null"
# Note: We're not going to use the storage account snapshot in dev, so setting it to null
storageaccount_snapshot         = "null"
prisma_settings_managerid       = "NBA-Game-224"
quortex_settings_accountid      = "nba"
azure_subscription_id           = "d2fa45a3-7718-4a48-93a5-86269b705ba4"
azure_vidplat_cosmosdb_rg       = "ott-diue2-vidplat-rg001"
azure_vidplat_cosmosdb_name     = "ott-diue2-vidplat-cdb001"
aws_vidplat_mongodb_name        = "dev-vidplat-main"
azure_vidplat_cosmosdb_endpoint = "https://ott-diue2-vidplat-cdb001.documents.azure.com:443/"
azure_tenant_id                 = "e898ff4a-4b69-45ee-a3ae-1cd6f239feb2"
eks_cluster_id                  = "2E0FF5FE0ED2B667AD2CEB3C99646FF6"
tvp_settings_authendpoint       = "https://appgw-stsssl.prodc.nba.tv3cloud.com/certactive"
ecms_base_url                   = "https://manage-dev.nba.com/wp-json/internal/api/v1"
quortex_settings_token_endpoint = "https://api.quortex.io/1.0/token/"
eventgrid_eg1                   = "https://ott-diue2-vidplat-evg001.eastus2-1.eventgrid.azure.net/api/events"
eventgrid_eg2                   = "https://ott-diue2-vidplat-evg002.eastus2-1.eventgrid.azure.net/api/events"
ecr_names = [
  "mst/orchestrator-aquilaactor-healthreporter",
  "mst/orchestrator-aquilaactor-processor",
  "mst/orchestrator-aquilawatchdog-channel-job",
  "mst/orchestrator-aquilawatchdog-processor",
  "mst/orchestrator-aquilawatchdog-source-job",
  "mst/orchestrator-aquilawatchdog-whitelist-job",
  "mst/orchestrator-dmmactor-api",
  "mst/orchestrator-dmmactor-healthreporter",
  "mst/orchestrator-dmmactor-processor",
  "mst/orchestrator-gmsinterpreter-api",
  "mst/orchestrator-gmsinterpreter-healthreporter",
  "mst/orchestrator-gmsinterpreter-processor",
  "mst/orchestrator-gmsinterpreter-verifyencoder",
  "mst/orchestrator-gmswatchdog-api",
  "mst/orchestrator-gmswatchdog-event-polling-job",
  "mst/orchestrator-gmswatchdog-game-polling-job",
  "mst/orchestrator-gmswatchdog-processor",
  "mst/orchestrator-gmswatchdog-reingest-liveevent-today-job",
  "mst/orchestrator-gmswatchdog-snapshotbuilder-job",
  "mst/orchestrator-gmswatchdog-teamzip-polling-job",
  "mst/orchestrator-orchestrator-processor",
  "mst/orchestrator-prismaactor-healthreporter",
  "mst/orchestrator-prismaactor-processor",
  "mst/orchestrator-scheduler-api",
  "mst/orchestrator-scheduler-serializer-processor",
  "mst/orchestrator-scheduler-synchronizationcron",
  "mst/orchestrator-streammarker-api",
  "mst/orchestrator-streammarker-processor",
  "mst/orchestrator-synamedia-endpoint-monitor",
  "mst/orchestrator-synamedia-input-monitor",
  "mst/orchestrator-thirdpartyactor-processor",
  "mst/orchestrator-tvpactor-api",
  "mst/orchestrator-tvpactor-processor",
  "mst/orchestrator-global-health-cron"
]
gms_endpoint = "https://gms-qa.internal.nba.com/"