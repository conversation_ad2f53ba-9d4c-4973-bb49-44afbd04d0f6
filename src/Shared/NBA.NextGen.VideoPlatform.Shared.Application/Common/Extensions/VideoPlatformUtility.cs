// "//-----------------------------------------------------------------------".
// <copyright file="VideoPlatformUtility.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using FluentValidation;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using TimeZoneConverter;

    /// <summary>
    /// The VideoPlatformUtility.
    /// </summary>
    /// <seealso cref="NBA.NextGen.Shared.Application.Common.UtilityExtensions" />
    public static class VideoPlatformUtility
    {
        /// <summary>
        /// Tries to return the enum equivalent of the string or null if it fails.
        /// </summary>
        /// <typeparam name="T">The enum type.</typeparam>
        /// <param name="value">The string value.</param>
        /// <returns>The Enum equivalent of the string or null if it fails.</returns>
        public static T? ToEnumOrNull<T>(this string value)
            where T : struct
        {
            try
            {
                return value.ToEnumMember<T>();
            }
            catch (InvalidOperationException)
            {
                return null;
            }
        }

        /// <summary>
        /// Parses multiple validation error messages present in a <see cref="ValidationException"/> into a single readable text line.
        /// </summary>
        /// <param name="validationException">The <see cref="ValidationException"/>.</param>
        /// <returns>A single readeable text line.</returns>
        public static string ParseValidationErrors([NotNull] this ValidationException validationException)
        {
            validationException.Required(nameof(validationException));
            var errorMessages = validationException.Errors.Select(x => $"{x.ErrorMessage}. ");
            var errorMessage = string.Concat(errorMessages).Trim();

            return errorMessage;
        }

        /// <summary>
        /// Converts a string to the equivalent value of a matching field. Useful to handle case insensitive scenarios.
        /// </summary>
        /// <param name="source">The source string that needs to match a field name.</param>
        /// <param name="targetType">The type that holds the field to match.</param>
        /// <returns>The matching field value or null if there is no matching.</returns>
        public static string ToStaticFieldValue([NotNull] this string source, [NotNull] Type targetType)
        {
            var fields = targetType.GetFields();

            foreach (var field in fields.Where(x => x.FieldType == typeof(string)))
            {
                var value = field.GetValue(null)?.ToString();

                if (value != null && value.Equals(source, StringComparison.OrdinalIgnoreCase))
                {
                    return value;
                }
            }

            return null;
        }

        /// <summary>
        /// Converts a DateTimeOffset to Eastern Time Zone.
        /// </summary>
        /// <param name="dateTimeOffset">The date time offset in Eastern Time Zone.</param>
        /// <returns>Returns date time offset in Eastern Time Zone.</returns>
        public static DateTimeOffset ToEasternTimeZone(this DateTimeOffset dateTimeOffset)
        {
            TimeZoneInfo easternTimeZoneInfo = TZConvert.GetTimeZoneInfo("Eastern Standard Time");
            return TimeZoneInfo.ConvertTime(dateTimeOffset, easternTimeZoneInfo);
        }

        /// <summary>
        /// Performs a Distinct By operation, using the key selector.
        /// </summary>
        /// <typeparam name="TSource">The type of the source.</typeparam>
        /// <typeparam name="TKey">The type of the key.</typeparam>
        /// <param name="source">The source.</param>
        /// <param name="keySelector">The key selector.</param>
        /// <returns>The list of distinct elements by the key selector.</returns>
        public static IEnumerable<TSource> DistinctByOperation<TSource, TKey>([NotNull] this IEnumerable<TSource> source, [NotNull] Func<TSource, TKey> keySelector)
        {
            source.Required(nameof(source));
            keySelector.Required(nameof(keySelector));
            HashSet<TKey> seenKeys = new HashSet<TKey>();
            foreach (TSource element in source)
            {
                if (seenKeys.Add(keySelector(element)))
                {
                    yield return element;
                }
            }
        }

        /// <summary>
        /// Gets the value associated with the specified key, or default if the dictionary doesn't contain an element with the specified key.
        /// </summary>
        /// <typeparam name="TKey">The type of keys in the dictionary.</typeparam>
        /// <typeparam name="TValue">The type of values in the dictionary.</typeparam>
        /// <param name="dictionary">The dictionary.</param>
        /// <param name="key">The key.</param>
        /// <returns>The value associated with the specified key, or the default value.</returns>
        public static TValue GetValueOrDefault<TKey, TValue>([NotNull] this IDictionary<TKey, TValue> dictionary, TKey key)
        {
            dictionary.Required(nameof(dictionary));
            return GetValueOrDefault(dictionary, key, default);
        }

        /// <summary>
        /// Gets the value associated with the specified key, or <paramref name="valueIfKeyNotFound"/> if the dictionary doesn't contain an element with the specified key.
        /// </summary>
        /// <typeparam name="TKey">The type of keys in the dictionary.</typeparam>
        /// <typeparam name="TValue">The type of values in the dictionary.</typeparam>
        /// <param name="dictionary">The dictionary.</param>
        /// <param name="key">The key.</param>
        /// <param name="valueIfKeyNotFound">The value if the dictionary doesn't contain an element with the specified key.</param>
        /// <returns>The value associated with the specified key, or the default value.</returns>
        public static TValue GetValueOrDefault<TKey, TValue>([NotNull] this IDictionary<TKey, TValue> dictionary, TKey key, TValue valueIfKeyNotFound)
        {
            dictionary.Required(nameof(dictionary));
            if (dictionary.TryGetValue(key, out TValue value))
            {
                return value;
            }

            return valueIfKeyNotFound;
        }

        /// <summary>
        /// Splits a string into substrings that are based on the separator string and then removes all leading and trailing white-space characters.
        /// </summary>
        /// <param name="concatenatedString">Contains the substrings to be split.</param>
        /// <param name="separator">Delimits the substrings in the string.</param>
        /// <returns>The trimmed substrings, discarding any empty substring.</returns>
        public static IEnumerable<string> SplitAndTrim([NotNull] this string concatenatedString, string separator)
        {
            var substrings = concatenatedString.Split(separator);
            var trimmedSubstrings = substrings
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrWhiteSpace(x));

            return trimmedSubstrings;
        }
    }
    // Adding note to force changes to all Shared services.
}
