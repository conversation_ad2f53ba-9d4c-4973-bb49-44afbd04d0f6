// "//-----------------------------------------------------------------------".
// <copyright file="AquilaClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Aquila.Services
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Shared.Domain.Enums;
    using NBA.NextGen.Vendor.Api.MkAquila;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Configuration;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;

    /// <summary>
    /// Sources.
    /// </summary>
    public class AquilaClientService : IAquilaClientService
    {
        /// <summary>
        /// The aquila client.
        /// </summary>
        private readonly IAquilaClient aquilaClient;

        /// <summary>
        /// The apim options.
        /// </summary>
        private readonly IOptionsMonitor<ApiManagementOptions> apimOptions;

        /// <summary>
        /// The aquila options.
        /// </summary>
        private readonly IOptionsMonitor<AquilaOptions> aquilaOptions;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<AquilaClientService> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="AquilaClientService" /> class.
        /// </summary>
        /// <param name="aquilaClientFactory">The aquila client factory.</param>
        /// <param name="apimOptions">The apim options.</param>
        /// <param name="aquilaOptions">The aquila options.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="logger">The logger.</param>
        public AquilaClientService([NotNull] IClientFactory aquilaClientFactory, IOptionsMonitor<ApiManagementOptions> apimOptions, IOptionsMonitor<AquilaOptions> aquilaOptions, IMapper mapper, ILogger<AquilaClientService> logger)
        {
            aquilaClientFactory.Required(nameof(aquilaClientFactory));
            this.aquilaClient = aquilaClientFactory.CreateClient(false);
            this.apimOptions = apimOptions;
            this.aquilaOptions = aquilaOptions;
            this.mapper = mapper;
            this.logger = logger;
            this.aquilaClient.SetPrepareRequestAction((httpRequestMessage) =>
            {
                httpRequestMessage.Headers.Add("Ocp-Apim-Subscription-Key", this.apimOptions.CurrentValue.SubscriptionKey);
                if (this.apimOptions.CurrentValue.EnableMocking)
                {
                    httpRequestMessage.Headers.Add("x-mock-data", "test");
                }

                return Task.CompletedTask;
            });
        }

        /// <inheritdoc/>
        public async Task<Channel> GetChannelByIdAsync(string channelId)
        {
            this.logger.LogInformation("Initiating Get Channel in aquila service for channel Id {ChannelId}", channelId);
            var aquilaResponse = await this.aquilaClient.ChannelGetAsync(this.aquilaOptions.CurrentValue.AccountId, channelId, CancellationToken.None).ConfigureAwait(false);
            this.logger.LogInformation("Successfully completed Get Channel in aquila service for channel Id {ChannelId}", channelId);
            return this.mapper.Map<Channel>(aquilaResponse);
        }

        /// <summary>
        /// Gets the channels as asynchronous operation.
        /// </summary>
        /// <returns>
        /// IEnumerable of <see cref="Channel"/>.
        /// </returns>
        public async Task<IEnumerable<Channel>> GetChannelsAsync()
        {
            var aquilaResponse = await this.aquilaClient.ChannelsGetAsync(this.aquilaOptions.CurrentValue.AccountId, CancellationToken.None).ConfigureAwait(false);
            var channels = new List<Channel>();
            var mappedSources = this.mapper.Map<IList<Channel>>(aquilaResponse.ToList());
            channels.AddRange(mappedSources);
            return channels;
        }

        /// <summary>
        /// Tries to get channel by identifier asynchronous.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <returns>retuns.</returns>
        public async Task<Channel> TryGetChannelByIdAsync(string channelId)
        {
            try
            {
                this.logger.LogInformation("Initiating Get Channel in aquila service for channel Id {ChannelId}", channelId);
                Channel channel = await this.GetChannelByIdAsync(channelId).ConfigureAwait(true);
                this.logger.LogInformation("Successfully completed Get Channel in aquila service for channel Id {ChannelId}", channelId);
                return channel;
            }
            catch (AquilaClientException ex)
            {
                this.logger.LogError(ex, "Failed to Get Channel in aquila service for channel Id {ChannelId}", channelId);
                return null;
            }
        }

        /// <inheritdoc/>
        public async Task<Source> GetSourceByIdAsync(string sourceId)
        {
            var aquilaResponse = await this.aquilaClient.SourceGetAsync(this.aquilaOptions.CurrentValue.AccountId, sourceId, CancellationToken.None).ConfigureAwait(false);
            var mappedSource = this.mapper.Map<Source>(aquilaResponse);
            return mappedSource;
        }

        /// <summary>
        /// Gets the source asynchronous.
        /// </summary>
        /// <returns>
        /// The sources.
        /// </returns>
        public async Task<IEnumerable<Source>> GetSourcesAsync()
        {
            var aquilaResponse = await this.aquilaClient.SourcesGetAsync(this.aquilaOptions.CurrentValue.AccountId, null, CancellationToken.None).ConfigureAwait(false);
            var sources = new List<Source>();
            var mappedSources = this.mapper.Map<IList<Source>>(aquilaResponse.ToList());
            sources.AddRange(mappedSources);
            return sources;
        }

        /// <summary>
        /// Gets the templates asynchronous.
        /// </summary>
        /// <returns>
        /// The templates.
        /// </returns>
        public async Task<IEnumerable<Template>> GetTemplatesAsync()
        {
            var aquilaResponse = await this.aquilaClient.TemplateChannelsGetAsync(this.aquilaOptions.CurrentValue.AccountId, CancellationToken.None).ConfigureAwait(false);
            var templates = new List<Template>();
            var mappedTemplates = this.mapper.Map<IList<Template>>(aquilaResponse.ToList());
            templates.AddRange(mappedTemplates);
            return templates;
        }

        /// <summary>
        /// Gets the sources asynchronous.
        /// </summary>
        /// <returns>
        /// The sources.
        /// </returns>
        public async Task<IEnumerable<Whitelist>> GetWhitelistsAsync()
        {
            var aquilaResponse = await this.aquilaClient.WhitelistgroupsGetAsync(this.aquilaOptions.CurrentValue.AccountId, CancellationToken.None).ConfigureAwait(false);
            var whitelists = new List<Whitelist>();
            var mappedSources = this.mapper.Map<IList<Whitelist>>(aquilaResponse.ToList());
            whitelists.AddRange(mappedSources);
            return whitelists;
        }

        /// <summary>
        /// Creates the whitelist.
        /// </summary>
        /// <param name="whitelist">The whitelist.</param>
        /// <param name="bookId">The bookId.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task UpdateWhitelistAsync(Whitelist whitelist, string bookId)
        {
            var mappedWhitelist = this.mapper.Map<WhitelistGroupApiPostSchema>(whitelist);
            await this.aquilaClient.WhitelistgroupPutAsync(this.aquilaOptions.CurrentValue.AccountId, bookId, mappedWhitelist, CancellationToken.None).ConfigureAwait(false);
        }

        /// <inheritdoc/>
        public async Task CreateWhitelistAsync(Whitelist whitelist)
        {
            var mappedWhitelist = this.mapper.Map<WhitelistGroupApiPostSchema>(whitelist);
            await this.aquilaClient.WhitelistgroupPostAsync(this.aquilaOptions.CurrentValue.AccountId, mappedWhitelist, CancellationToken.None).ConfigureAwait(false);
        }

        /// <inheritdoc/>
        public async Task StartChannelInstanceAsync(string channelId, string instanceId)
        {
            this.logger.LogInformation($"Requesting start {nameof(ChannelInstance)} {{InstanceId}} for {nameof(Channel)} {{ChannelId}} in {nameof(AquilaClientService)}", channelId, instanceId);
            await this.aquilaClient.ChannelStartPostAsync(this.aquilaOptions.CurrentValue.AccountId, channelId, new ChannelApiStartStopSchema { InstanceId = instanceId }, CancellationToken.None).ConfigureAwait(false);
            this.logger.LogInformation($"Requested start {nameof(ChannelInstance)} {{InstanceId}} for {nameof(Channel)} {{ChannelId}} in {nameof(AquilaClientService)}", channelId, instanceId);
        }

        /// <inheritdoc/>
        public async Task StopChannelInstanceAsync(string channelId, string instanceId)
        {
            this.logger.LogInformation($"Requesting stop {nameof(ChannelInstance)} {{InstanceId}} for {nameof(Channel)} {{ChannelId}} in {nameof(AquilaClientService)}", channelId, instanceId);
            await this.aquilaClient.ChannelStopPostAsync(this.aquilaOptions.CurrentValue.AccountId, channelId, new ChannelApiStartStopSchema { InstanceId = instanceId }, CancellationToken.None).ConfigureAwait(false);
            this.logger.LogInformation($"Requested stop {nameof(ChannelInstance)} {{InstanceId}} for {nameof(Channel)} {{ChannelId}} in {nameof(AquilaClientService)}", channelId, instanceId);
        }

        /// <inheritdoc/>
        public async Task CreateChannelAsync([NotNull] ChannelCreationInfo channel)
        {
            channel.Required(nameof(channel));
            await this.SetTemplateAsync(channel, "create").ConfigureAwait(false);
            await this.SetSourcesAsync(channel, "create").ConfigureAwait(false);

            var channelApiPost = this.mapper.Map<ChannelApiPostSchema>(channel);
            this.logger.LogInformation("Initiating Create Channel in aquila service for channel Id {ChannelId}", channel.ChannelId);
            await this.aquilaClient.ChannelPostAsync(this.aquilaOptions.CurrentValue.AccountId, false, channelApiPost).ConfigureAwait(false);
            this.logger.LogInformation("Successfully Created Channel in aquila service for channel Id {ChannelId}", channel.ChannelId);
        }

        /// <inheritdoc/>
        public async Task DeleteChannelAsync(string channelId)
        {
            this.logger.LogInformation("AquilaClientService: Initiating Delete Channel in aquila service for channel Id {ChannelId} with AccountId {AccountId}",
                channelId, this.aquilaOptions.CurrentValue.AccountId);

            try
            {
                await this.aquilaClient.ChannelDeleteAsync(this.aquilaOptions.CurrentValue.AccountId, channelId, CancellationToken.None).ConfigureAwait(false);
                this.logger.LogInformation("AquilaClientService: Successfully completed ChannelDeleteAsync for channel Id {ChannelId}", channelId);
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "AquilaClientService: Error during ChannelDeleteAsync for channel Id {ChannelId}: {ErrorMessage}",
                    channelId, ex.Message);
                throw;
            }
        }

        /// <inheritdoc/>
        public async Task UpdateChannelAsync([NotNull] ChannelCreationInfo channel)
        {
            channel.Required(nameof(channel));
            await this.SetTemplateAsync(channel, "update").ConfigureAwait(false);
            await this.SetSourcesAsync(channel, "update").ConfigureAwait(false);

            var channelToUpdate = this.mapper.Map<ChannelApiPutSchema>(channel);
            this.logger.LogInformation("Initiating Update Channel in aquila service for channel Id {ChannelId}", channel.ChannelId);
            await this.aquilaClient.ChannelPutAsync(this.aquilaOptions.CurrentValue.AccountId, channelToUpdate.Id, channelToUpdate).ConfigureAwait(false);
            this.logger.LogInformation("Successfully Updated Channel in aquila service for channel Id {ChannelId}", channel.ChannelId);
        }

        /// <inheritdoc/>
        public async Task<HealthStatusResult> GetHealthStatusAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _ = await this.aquilaClient.SourcesGetAsync(this.aquilaOptions.CurrentValue.AccountId, cancellationToken: cancellationToken).ConfigureAwait(false);
                return new HealthStatusResult { Status = HealthStatus.Healthy };
            }
            catch (AquilaClientException exception)
            {
                return new HealthStatusResult { Status = HealthStatus.Unhealthy, Exception = exception };
            }
        }

        /// <summary>
        /// Set the template name asynchronous.
        /// </summary>
        /// <param name="channel">The channel.</param>
        /// <param name="action">The action.</param>
        /// <exception cref="System.ArgumentException">
        /// There is no template with {channelTemplateName} name in Aquila.
        /// or
        /// There is no BackupSourceName with {backupSourceName} name in Aquila.
        /// or
        /// There is no MainSourceName with {mainSourceName} name in Aquila.
        /// </exception>
        /// <returns>Task.</returns>
        private async Task SetTemplateAsync(ChannelCreationInfo channel, string action)
        {
            var templates = await this.aquilaClient.TemplateChannelsGetAsync(this.aquilaOptions.CurrentValue.AccountId).ConfigureAwait(false);
            var templateName = channel.TemplateName;
            channel.TemplateName = templates
                .SingleOrDefault(x => x.Name == channel.TemplateName)?.Id;

            if (string.IsNullOrEmpty(channel.TemplateName))
            {
                this.logger.LogCritical(
                    "Trying to {action} channel with id {AquilaChannelId} using template with id '{TemplateId}', but there is no template with that name in Aquila.",
                    action,
                    channel.ChannelId,
                    templateName);
                throw new ArgumentException($"Cannot {action} {channel.ChannelName} {channel.ChannelId} with template {templateName} because there is no template with that name in Aquila");
            }
        }

        /// <summary>
        /// Retrieves the sources asynchronous.
        /// </summary>
        /// <param name="channel">The channel.</param>
        /// <param name="action">The action.</param>
        /// <returns>Task.</returns>
        private async Task SetSourcesAsync(ChannelCreationInfo channel, string action)
        {
            this.ValidateChannelSources(channel, action);
            var sourcesApi = await this.aquilaClient.SourcesGetAsync(this.aquilaOptions.CurrentValue.AccountId).ConfigureAwait(false);

            foreach (var aquilaChannelSource in channel.Sources)
            {
                aquilaChannelSource.MainSourceName = this.GetMainSource(aquilaChannelSource.MainSourceName, channel, sourcesApi, action);

                var backupSource = this.GetBackupSource(aquilaChannelSource.BackupSourceName, channel, sourcesApi, action);
                aquilaChannelSource.BackupSourceName = backupSource != null ? backupSource : string.Empty;
            }
        }

        /// <summary>
        /// Validate the Sources in <see cref="ChannelCreationInfo"/>.
        /// </summary>
        /// <param name="channel">The channel.</param>
        /// <param name="action">The action.</param>
        /// <exception cref="ArgumentException">
        /// There is no MainSourceName with {sourceName} name in Aquila.
        /// </exception>
        private void ValidateChannelSources(ChannelCreationInfo channel, string action)
        {
            if (channel.Sources == null)
            {
                throw new ArgumentException($"Cannot {action} {channel.ChannelName} {channel.ChannelId} because there is no source");
            }
        }

        /// <summary>
        /// Get the main source id.
        /// </summary>
        /// <param name="sourceName">The source name.</param>
        /// <param name="channel">The channel.</param>
        /// <param name="sourcesApi">The sources from API.</param>
        /// <param name="action">The action.</param>
        /// <exception cref="ArgumentException">
        /// There is no MainSourceName with {sourceName} name in Aquila.
        /// </exception>
        /// <returns>The source id.</returns>
        private string GetMainSource(string sourceName, ChannelCreationInfo channel, ICollection<SourceApiGetSchema> sourcesApi, string action)
        {
            var mainSource = this.GetSourceIdBySourceName(sourceName, sourcesApi);
            if (mainSource == null)
            {
                this.logger.LogCritical(
                    "Trying to {action} channel with id {AquilaChannelId} using main source with name '{SourceName}', but there is no source with that name in Aquila.",
                    action,
                    channel.ChannelId,
                    sourceName);
                throw new ArgumentException($"Cannot {action} {channel.ChannelName} {channel.ChannelId} with main source {sourceName} because there is no source with that name in Aquila");
            }

            return mainSource;
        }

        /// <summary>
        /// Get the backup source id.
        /// </summary>
        /// <param name="sourceName">The source name.</param>
        /// <param name="channel">The channel.</param>
        /// <param name="sourcesApi">The sources from API.</param>
        /// <param name="action">The action.</param>
        /// <returns>The source id.</returns>
        private string GetBackupSource(string sourceName, ChannelCreationInfo channel, ICollection<SourceApiGetSchema> sourcesApi, string action)
        {
            var backupSource = this.GetSourceIdBySourceName(sourceName, sourcesApi);
            if (backupSource == null)
            {
                this.logger.LogWarning(
                    "Trying to {action} channel with id {AquilaChannelId} using backup source with name '{SourceName}', but there is no source with that name in Aquila.",
                    action,
                    channel.ChannelId,
                    sourceName);
            }

            return backupSource;
        }

        /// <summary>
        /// Get the source id by source name.
        /// </summary>
        /// <param name="sourceName">The source name.</param>
        /// <param name="sourcesApi">The sources from API.</param>
        /// <returns>The source id.</returns>
        private string GetSourceIdBySourceName(string sourceName, ICollection<SourceApiGetSchema> sourcesApi)
        {
            var sourceId = sourcesApi
                .SingleOrDefault(x => x.Name == sourceName)?.Id;
            return sourceId;
        }
    }
}
