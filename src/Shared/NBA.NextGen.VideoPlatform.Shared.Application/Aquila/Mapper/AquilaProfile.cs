// "//-----------------------------------------------------------------------".
// <copyright file="AquilaProfile.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Mapper
{
    using System.Linq;
    using AutoMapper;
    using NBA.NextGen.Vendor.Api.MkAquila;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;

    /// <summary>
    ///  Aquila Profile AutoMapper.
    /// </summary>
    /// <seealso cref="AutoMapper.Profile" />
    public class AquilaProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="AquilaProfile"/> class.
        /// </summary>
        public AquilaProfile()
        {
            this.CreateMap<Instances, ChannelInstance>();

            this.CreateMap<ChannelApiSchema, Channel>()
                .ForMember(x => x.Instances, y => y.MapFrom(z => z.Status.Instances))
                .ForMember(x => x.AccountId, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore())
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore());

            this.CreateMap<SourceApiGetSchemaIpAddressesItem, IpAddress>()
                .ForMember(x => x.Address, y => y.MapFrom(z => z.IpAddress));

            this.CreateMap<SourceApiGetSchema, Source>()
                .ForMember(x => x.AccountId, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore())
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore());

            this.CreateMap<TemplateApiSchema, Template>()
                .ForMember(x => x.QuotaName, y => y.MapFrom(z => z.Quota_name))
                .ForMember(x => x.AccountId, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore())
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore());

            this.CreateMap<WhitelistGroupApiGetSchemaEntriesItem, Entry>()
                .ForMember(x => x.AccountId, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore())
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore());

            this.CreateMap<Entry, WhitelistGroupApiPostSchemaEntriesItem>()
                .ForMember(x => x.AdditionalProperties, y => y.Ignore());

            this.CreateMap<WhitelistGroupApiGetSchema, Whitelist>()
                .ForMember(x => x.AccountId, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore())
                .ForMember(x => x.Entries, y => y.MapFrom(z => z.Entries))
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore());

            this.CreateMap<Whitelist, WhitelistGroupApiPostSchema>()
                .ForMember(x => x.AdditionalProperties, y => y.Ignore());

            this.CreateMap<SourceApiPostSchemaIpAddressesItem, IpAddress>()
                .ForMember(x => x.Address, y => y.MapFrom(z => z.IpAddress));

            this.CreateMap<SourceApiPostSchema, Source>()
                .ForMember(x => x.InputMode, y => y.MapFrom(z => z.InputMode.ToString()))
                .ForMember(x => x.IpAddresses, y => y.MapFrom(z => z.IpAddresses.AsEnumerable()))
                .ForMember(x => x.Properties, y => y.MapFrom(z => z.Properties))
                .ForMember(x => x.AccountId, y => y.Ignore())
                .ForMember(x => x.NotificationState, y => y.Ignore())
                .ForMember(x => x.ConcurrencyToken, y => y.Ignore())
                .ForMember(x => x.Type, y => y.Ignore());
        }
        // Adding a small change to force changes to all Shared services.
    }
}
