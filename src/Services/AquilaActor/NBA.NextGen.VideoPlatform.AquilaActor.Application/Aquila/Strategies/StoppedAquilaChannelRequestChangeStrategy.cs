// "//-----------------------------------------------------------------------".
// <copyright file="StoppedAquilaChannelRequestChangeStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <inheritdoc/>
    public class StoppedAquilaChannelRequestChangeStrategy : IAquilaRequestChangeStrategy
    {
        /// <summary>
        /// The media service.
        /// </summary>
        private readonly IAquilaClientService aquilaClientService;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<StoppedAquilaChannelRequestChangeStrategy> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="StoppedAquilaChannelRequestChangeStrategy"/> class.
        /// </summary>
        /// <param name="aquilaClientService">The media service.</param>
        /// <param name="telemetryService">The telemetry service.</param>
        /// <param name="logger">The logger.</param>
        public StoppedAquilaChannelRequestChangeStrategy(IAquilaClientService aquilaClientService, ITelemetryService telemetryService, ILogger<StoppedAquilaChannelRequestChangeStrategy> logger)
        {
            this.aquilaClientService = aquilaClientService;
            this.telemetryService = telemetryService;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public AquilaChannelState ChannelState => AquilaChannelState.Stopped;

        /// <inheritdoc/>
        public async Task RequestChangeAsync(AquilaChannelState desiredAquilaChannelState, string instanceId, string channelId, string eventId)
        {
            switch (desiredAquilaChannelState)
            {
                case AquilaChannelState.Started:
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToStarted, EventData.CorrelationTag);
                    await this.aquilaClientService.StartChannelInstanceAsync(channelId, instanceId).ConfigureAwait(false);
                    break;
                case AquilaChannelState.Starting:
                    await this.aquilaClientService.StartChannelInstanceAsync(channelId, instanceId).ConfigureAwait(false);
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToStarting, EventData.CorrelationTag);
                    break;
                case AquilaChannelState.Stopping:
                    // nothing.
                    break;
                case AquilaChannelState.Stopped:
                    // nothing.
                    break;
                case AquilaChannelState.Deleted:
                    this.logger.LogInformation("StoppedAquilaChannelRequestChangeStrategy: Starting deletion for channel {ChannelId}", channelId);
                    await this.aquilaClientService.DeleteChannelAsync(channelId).ConfigureAwait(false);
                    this.logger.LogInformation("StoppedAquilaChannelRequestChangeStrategy: Successfully completed DeleteChannelAsync for channel {ChannelId}", channelId);
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToDeleted, EventData.CorrelationTag);
                    break;
                default:
                    throw new NotSupportedException();
            }
        }
    }
}
